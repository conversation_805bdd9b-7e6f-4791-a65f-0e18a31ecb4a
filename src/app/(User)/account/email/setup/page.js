'use client';
import { Col, Row } from "react-bootstrap";
import React, { useState, useEffect } from 'react'
import AccountLayout from "@/Layouts/AccountLayout";
import MetaHead from "@/Seo/Meta/MetaHead";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import TextInput from "@/Components/UI/TextInput";
import StatusIndicator from "@/Components/UI/StatusIndicator";
import { put, get } from "@/utils/apiUtils";
import { useRouter, useSearchParams } from 'next/navigation';
import { useSelector } from 'react-redux';
import { hashInput } from "@/utils/hashInput";
import { v4 as uuidv4 } from "uuid";
import "@/css/account/AccountDetails.scss";

export default function SetupEmail() {
    const [currentEmail, setCurrentEmail] = useState('');
    const [newEmail, setNewEmail] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [saveStatus, setSaveStatus] = useState(null);
    const [error, setError] = useState(null);
    const [userData, setUserData] = useState(null);
    const [isLoadingUserData, setIsLoadingUserData] = useState(true);

    const router = useRouter();
    const searchParams = useSearchParams();
    const reduxUser = useSelector((state) => state?.auth?.user || null);

    const metaArray = {
        noindex: true,
        title: "Setup Email Address | Update Info | TradeReply",
        description: "Update your email address on TradeReply.com.",
        canonical_link: "https://www.tradereply.com/account/details",
        og_site_name: "TradeReply",
        og_title: "Setup Email Address | Update Info | TradeReply",
        og_description: "Update your email address on TradeReply.com.",
        twitter_title: "Setup Email Address | Update Info | TradeReply",
        twitter_description: "Update your email address on TradeReply.com.",
    };

    const handleSave = async () => {
        if (!email.trim()) {
            setSaveStatus('error');
            setError('Email address is required');
            setTimeout(() => setSaveStatus(null), 3000);
            return;
        }

        try {
            setIsLoading(true);
            setSaveStatus('loading');
            setError(null);

            const response = await put('/account/email/update', {
                email: email.trim(),
            });

            if (response.success) {
                setSaveStatus('success');
                setTimeout(() => setSaveStatus(null), 3000);
            } else {
                throw new Error(response.message || 'Failed to update email address');
            }
        } catch (err) {
            console.error('Email update error:', err);
            const errorMessage = err.response?.data?.message || err.message || 'Failed to update email address. Please try again.';
            setSaveStatus('error');
            setError(errorMessage);
            setTimeout(() => setSaveStatus(null), 3000);
        } finally {
            setIsLoading(false);
        }
    };

    const handleCancel = () => {
        setEmail('');
        setError(null);
        setSaveStatus(null);
    };
    return (
        <>
            <AccountLayout>
                <MetaHead props={metaArray} />
                <div className="account_setup_email">
                    <SidebarHeading title="New Email Address" />
                    <div className="mb-3 mb-lg-4">
                        <div className="common_blackcard account_card">
                            <div className="common_blackcard_innerheader">
                                <div className="common_blackcard_innerheader_content">
                                    <div className="account_header_main">
                                        <h6>Email</h6>
                                        <div className="account_status_indicator">
                                            <StatusIndicator
                                                saveStatus={saveStatus}
                                                error={error}
                                            />
                                        </div>
                                    </div>
                                    <p>Enter the email address you want associated with your TradeReply account. You will use this email address to log in.</p>
                                </div>
                            </div>
                            <div className="common_blackcard_innerbody">
                                <div className="account_card_list">
                                    <div className="col-lg-5 col-md-8 col-12">
                                        <TextInput
                                            type="email"
                                            placeholder="Enter your email address"
                                            value={email}
                                            onChange={(e) => setEmail(e.target.value)}
                                            disabled={isLoading}
                                        />
                                    </div>

                                    {error && saveStatus !== 'loading' && (
                                        <div className="mt-3">
                                            <p style={{ color: 'red', fontSize: '14px' }}>
                                                {error}
                                            </p>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                        <div className="account_card_list_btns mt-3">
                            <button
                                className="btn-style white-btn"
                                onClick={handleCancel}
                                disabled={isLoading}
                            >
                                Cancel
                            </button>
                            <button
                                className="btn-style"
                                onClick={handleSave}
                                disabled={isLoading || !email.trim()}
                            >
                                {isLoading ? 'Saving...' : 'Save'}
                            </button>
                        </div>
                    </div>

                </div>
            </AccountLayout>
        </>
    )
}
